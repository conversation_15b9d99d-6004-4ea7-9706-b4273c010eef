// Game Configuration and Template System
// This file contains all game data and category definitions for automated page generation

// Game Categories Configuration
const gameCategories = {
    'card-games': {
        name: 'Card Games',
        icon: '🃏',
        description: 'Classic card games including Blackjack variants, Hearts, Solitaire, FreeCell, and strategic card gameplay',
        games: ['hearts', 'solitaire', 'spider-solitaire', 'freecell', 'texas-holdem', 'spades']
    },
    'blackjack': {
        name: 'Blackjack Games',
        icon: '🎰',
        description: 'Professional Blackjack variants with different rules and strategies',
        games: ['blackjack', 'blackjack-online', 'free-bet-blackjack', 'pontoon']
    },
    'puzzle': {
        name: 'Puzzle Games',
        icon: '🧩',
        description: 'Mind-challenging puzzle games that test logic and problem-solving skills',
        games: ['sudoku-online', '2048', 'tetris-game', 'memoryGame']
    },
    'arcade': {
        name: 'Arcade Games',
        icon: '🕹️',
        description: 'Fast-paced action games with classic arcade gameplay',
        games: ['snake-game', 'breakout-game', 'reaction-test']
    },
    'relaxation': {
        name: 'Relaxation Games',
        icon: '🌈',
        description: 'Stress-relief and calming games for meditation and mindfulness',
        games: ['pop-it-game', 'fidget-spinner', 'breathing-ball', 'drawing-wall', 'bubble-float']
    },
    'strategy': {
        name: 'Strategy Games',
        icon: '♔',
        description: 'Deep thinking and strategic planning games including Chess with intelligent AI opponents',
        games: ['chess']
    },
    'other': {
        name: 'Other Games',
        icon: '🎮',
        description: 'Unique and creative games that don\'t fit traditional categories',
        games: ['particle-trail', 'typing-machine', 'virtual-pet']
    }
};

// Complete Game Data with Categories
const completeGameData = {
    // Blackjack Games
    'blackjack': { 
        name: 'Blackjack', 
        icon: '🎰', 
        rating: '4.9', 
        difficulty: '⭐⭐⭐',
        description: 'Master the classic 21-point card game with professional strategies and realistic casino atmosphere.',
        tags: ['Casino', 'Strategy', 'Cards'],
        url: '/blackjack',
        bgClass: 'blackjack-bg',
        category: 'blackjack',
        seoTitle: 'Play Blackjack Online - Professional Casino Card Game with Strategy Guide',
        seoDescription: 'Master professional Blackjack with our realistic casino game featuring optimal strategy, card counting practice, and authentic gameplay.'
    },
    'blackjack-online': { 
        name: 'Blackjack Practice', 
        icon: '🎯', 
        rating: '4.8', 
        difficulty: '⭐⭐⭐⭐',
        description: 'Learn basic strategy, practice card counting, and master optimal play decisions.',
        tags: ['Training', 'Strategy', 'Practice'],
        url: '/blackjack-online',
        bgClass: 'practice-bg',
        category: 'blackjack',
        seoTitle: 'Blackjack Practice Mode - Learn Optimal Strategy Risk-Free',
        seoDescription: 'Perfect your Blackjack skills with our practice mode featuring strategy hints, card counting training, and risk-free gameplay.'
    },
    'free-bet-blackjack': { 
        name: 'Free Bet Blackjack', 
        icon: '🆓', 
        rating: '4.7', 
        difficulty: '⭐⭐⭐⭐⭐',
        description: 'Advanced Blackjack variant with free double downs, free splits, and special dealer 22 push rules.',
        tags: ['Advanced', 'Free Bets', 'Casino'],
        url: '/free-bet-blackjack',
        bgClass: 'freebet-bg',
        category: 'blackjack',
        seoTitle: 'Free Bet Blackjack - Advanced Casino Variant with Free Double Downs',
        seoDescription: 'Experience Free Bet Blackjack with complimentary double downs, free splits, and unique dealer 22 push rules.'
    },
    'pontoon': { 
        name: 'Pontoon', 
        icon: '🚤', 
        rating: '4.6', 
        difficulty: '⭐⭐⭐⭐',
        description: 'Experience the British version of Blackjack with unique rules and terminology.',
        tags: ['British', 'Cards', 'Casino'],
        url: '/pontoon-card-game',
        bgClass: 'pontoon-bg',
        category: 'blackjack',
        seoTitle: 'Pontoon Card Game - British Blackjack Variant Online',
        seoDescription: 'Play Pontoon, the British version of Blackjack with unique rules, terminology, and strategic gameplay.'
    },

    // Card Games
    'hearts': { 
        name: 'Hearts', 
        icon: '♥️', 
        rating: '4.8', 
        difficulty: '⭐⭐⭐',
        description: 'Experience the classic trick-taking card game with advanced AI opponents and strategic gameplay.',
        tags: ['Cards', 'Strategy', 'Trick-taking'],
        url: '/hearts',
        bgClass: 'hearts-bg',
        category: 'card-games',
        seoTitle: 'Hearts Card Game Online - Classic Trick-Taking Strategy Game',
        seoDescription: 'Master Hearts card game with intelligent AI opponents, strategic gameplay, and classic trick-taking mechanics.'
    },
    'solitaire': {
        name: 'Solitaire',
        icon: '🃏',
        rating: '4.6',
        difficulty: '⭐⭐⭐',
        description: 'Classic single-player card game with multiple variations and difficulty levels.',
        tags: ['Cards', 'Single Player', 'Classic'],
        url: '/klondike-solitaire',
        bgClass: 'solitaire-bg',
        category: 'card-games',
        seoTitle: 'Klondike Solitaire Online - Classic Single Player Card Game',
        seoDescription: 'Play classic Klondike Solitaire with smooth animations, multiple difficulty levels, and traditional gameplay.'
    },
    'spider-solitaire': {
        name: 'Spider Solitaire',
        icon: '🕷️',
        rating: '4.5',
        difficulty: '⭐⭐⭐⭐',
        description: 'Master the challenging Spider Solitaire with multiple difficulty levels and strategic gameplay.',
        tags: ['Cards', 'Strategy', 'Solitaire'],
        url: '/spider-solitaire',
        bgClass: 'spider-bg',
        category: 'card-games',
        seoTitle: 'Spider Solitaire Online - Challenging Multi-Suit Card Game',
        seoDescription: 'Challenge yourself with Spider Solitaire featuring multiple difficulty levels, strategic gameplay, and smooth card animations.'
    },
    'texas-holdem': {
        name: 'Texas Hold\'em',
        icon: '🎲',
        rating: '4.5',
        difficulty: '⭐⭐⭐⭐',
        description: 'Experience the world\'s most popular poker variant with realistic AI opponents.',
        tags: ['Poker', 'Strategy', 'Casino'],
        url: '/texas-holdem-game',
        bgClass: 'poker-bg',
        category: 'card-games',
        seoTitle: 'Texas Hold\'em Poker Online - World\'s Most Popular Poker Game',
        seoDescription: 'Master Texas Hold\'em poker with realistic AI opponents, professional gameplay, and authentic casino atmosphere.'
    },
    'spades': {
        name: 'Spades',
        icon: '♠️',
        rating: '4.7',
        difficulty: '⭐⭐⭐',
        description: 'Master the classic partnership card game with intelligent AI teammates and opponents.',
        tags: ['Cards', 'Partnership', 'Strategy'],
        url: '/spades',
        bgClass: 'spades-bg',
        category: 'card-games',
        seoTitle: 'Spades Card Game Online - Classic Partnership Strategy Game',
        seoDescription: 'Play Spades with intelligent AI partners, strategic bidding, and classic partnership card game mechanics.'
    },
    'freecell': {
        name: 'FreeCell Solitaire',
        icon: '🃏',
        rating: '4.8',
        difficulty: '⭐⭐⭐⭐',
        description: 'Master the strategic FreeCell Solitaire with four free cells and foundation building gameplay.',
        tags: ['Cards', 'Strategy', 'Solitaire'],
        url: '/freecell-solitaire',
        bgClass: 'freecell-bg',
        category: 'card-games',
        seoTitle: 'FreeCell Solitaire Online - Strategic Card Game with Free Cells',
        seoDescription: 'Play FreeCell Solitaire with four strategic free cells, foundation building, and challenging card organization gameplay.'
    },

    // Strategy Games
    'chess': {
        name: 'Chess',
        icon: '♔',
        rating: '4.9',
        difficulty: '⭐⭐⭐⭐⭐',
        description: 'Master the ancient game of strategy with intelligent AI opponents and comprehensive features.',
        tags: ['Strategy', 'Classic', 'AI'],
        url: '/chess',
        bgClass: 'chess-bg',
        category: 'strategy',
        seoTitle: 'Chess Online - Master Strategy Game with Intelligent AI Opponents',
        seoDescription: 'Play Chess with advanced AI opponents, multiple difficulty levels, beautiful 3D board, and comprehensive chess features.'
    },

    // Puzzle Games
    'sudoku-online': {
        name: 'Sudoku',
        icon: '🧩',
        rating: '4.8',
        difficulty: '⭐⭐⭐',
        description: 'Challenge your mind with classic number puzzle featuring multiple difficulty levels.',
        tags: ['Puzzle', 'Numbers', 'Logic'],
        url: '/sudoku-online',
        bgClass: 'sudoku-bg',
        category: 'puzzle',
        seoTitle: 'Sudoku Puzzle Online - Classic Number Logic Game',
        seoDescription: 'Solve Sudoku puzzles with multiple difficulty levels, hint system, and classic number logic gameplay.'
    },
    '2048': {
        name: '2048',
        icon: '🔢',
        rating: '4.7',
        difficulty: '⭐⭐⭐',
        description: 'Combine numbered tiles to reach 2048 in this addictive puzzle game.',
        tags: ['Puzzle', 'Numbers', 'Addictive'],
        url: '/2048',
        bgClass: 'numbers-bg',
        category: 'puzzle',
        seoTitle: '2048 Number Puzzle Game - Addictive Tile Combining Challenge',
        seoDescription: 'Play 2048 puzzle game with smooth animations, score tracking, and addictive tile-combining gameplay.'
    },
    'tetris-game': {
        name: 'Tetris',
        icon: '🟦',
        rating: '4.9',
        difficulty: '⭐⭐⭐',
        description: 'Classic falling blocks puzzle game with modern graphics and smooth gameplay.',
        tags: ['Puzzle', 'Classic', 'Arcade'],
        url: '/tetris-game',
        bgClass: 'tetris-bg',
        category: 'puzzle',
        seoTitle: 'Tetris Online - Classic Falling Blocks Puzzle Game',
        seoDescription: 'Play classic Tetris with modern graphics, smooth controls, and addictive falling blocks puzzle gameplay.'
    },
    'memoryGame': {
        name: 'Memory Game',
        icon: '🧠',
        rating: '4.4',
        difficulty: '⭐⭐',
        description: 'Test your memory with this classic card matching game.',
        tags: ['Memory', 'Cards', 'Brain Training'],
        url: '/memoryGame',
        bgClass: 'memory-bg',
        category: 'puzzle',
        seoTitle: 'Memory Card Game - Brain Training Matching Challenge',
        seoDescription: 'Improve your memory with classic card matching game featuring multiple difficulty levels and brain training.'
    },

    // Arcade Games
    'snake-game': {
        name: 'Snake',
        icon: '🐍',
        rating: '4.7',
        difficulty: '⭐⭐',
        description: 'Classic arcade game where you control a growing snake to collect food.',
        tags: ['Arcade', 'Classic', 'Retro'],
        url: '/snake-game',
        bgClass: 'snake-bg',
        category: 'arcade',
        seoTitle: 'Snake Game Online - Classic Arcade Retro Gaming',
        seoDescription: 'Play the classic Snake arcade game with smooth controls, score tracking, and nostalgic retro gameplay.'
    },
    'breakout-game': {
        name: 'Breakout',
        icon: '🧱',
        rating: '4.6',
        difficulty: '⭐⭐⭐',
        description: 'Break bricks with a bouncing ball in this classic arcade game.',
        tags: ['Arcade', 'Classic', 'Action'],
        url: '/breakout-game',
        bgClass: 'breakout-bg',
        category: 'arcade',
        seoTitle: 'Breakout Game Online - Classic Brick Breaking Arcade',
        seoDescription: 'Play Breakout arcade game with bouncing ball physics, power-ups, and classic brick-breaking action.'
    },
    'reaction-test': {
        name: 'Reaction Test',
        icon: '⚡',
        rating: '4.5',
        difficulty: '⭐',
        description: 'Test your reflexes and reaction time with this simple but challenging game.',
        tags: ['Reflex', 'Test', 'Speed'],
        url: '/reaction-test',
        bgClass: 'reaction-bg',
        category: 'arcade',
        seoTitle: 'Reaction Time Test - Measure Your Reflexes Online',
        seoDescription: 'Test your reaction time and reflexes with our accurate online reaction speed measurement tool.'
    },

    // Relaxation Games
    'pop-it-game': {
        name: 'Pop-it',
        icon: '🫧',
        rating: '4.4',
        difficulty: '⭐',
        description: 'Pop virtual bubbles for stress relief and relaxation in this calming sensory game.',
        tags: ['Relaxation', 'Stress Relief', 'Sensory'],
        url: '/pop-it-game',
        bgClass: 'popit-bg',
        category: 'relaxation',
        seoTitle: 'Pop-it Game Online - Virtual Bubble Popping for Stress Relief',
        seoDescription: 'Relax with virtual Pop-it bubble popping game featuring satisfying sounds, colorful animations, and stress relief.'
    },
    'fidget-spinner': {
        name: 'Fidget Spinner',
        icon: '🌀',
        rating: '4.3',
        difficulty: '⭐',
        description: 'Spin the virtual fidget spinner and watch mesmerizing patterns unfold.',
        tags: ['Relaxation', 'Fidget', 'Meditation'],
        url: '/fidget-spinner',
        bgClass: 'spinner-bg',
        category: 'relaxation',
        seoTitle: 'Fidget Spinner Online - Virtual Spinner for Focus and Relaxation',
        seoDescription: 'Spin virtual fidget spinner with realistic physics, mesmerizing patterns, and stress-relief benefits.'
    },
    'breathing-ball': {
        name: 'Breathing Ball',
        icon: '🌬️',
        rating: '4.6',
        difficulty: '⭐',
        description: 'Follow the breathing ball for guided meditation and relaxation exercises.',
        tags: ['Meditation', 'Breathing', 'Wellness'],
        url: '/breathing-ball',
        bgClass: 'breathing-bg',
        category: 'relaxation',
        seoTitle: 'Breathing Ball - Guided Meditation and Relaxation Exercise',
        seoDescription: 'Practice mindful breathing with our guided breathing ball for meditation, stress relief, and wellness.'
    },
    'drawing-wall': {
        name: 'Drawing Wall',
        icon: '🎨',
        rating: '4.2',
        difficulty: '⭐',
        description: 'Express your creativity with this digital drawing and painting canvas.',
        tags: ['Creative', 'Art', 'Drawing'],
        url: '/drawing-wall',
        bgClass: 'drawing-bg',
        category: 'relaxation',
        seoTitle: 'Digital Drawing Wall - Online Art Canvas for Creative Expression',
        seoDescription: 'Create digital art with our online drawing wall featuring brushes, colors, and creative tools.'
    },
    'bubble-float': {
        name: 'Bubble Float',
        icon: '💭',
        rating: '4.1',
        difficulty: '⭐',
        description: 'Watch peaceful bubbles float across your screen in this calming experience.',
        tags: ['Relaxation', 'Peaceful', 'Ambient'],
        url: '/bubble-float',
        bgClass: 'bubble-bg',
        category: 'relaxation',
        seoTitle: 'Bubble Float - Peaceful Floating Bubbles for Relaxation',
        seoDescription: 'Enjoy peaceful floating bubbles with calming animations and relaxing ambient experience.'
    },

    // Other Games
    'particle-trail': {
        name: 'Particle Trail',
        icon: '✨',
        rating: '4.3',
        difficulty: '⭐',
        description: 'Create beautiful particle effects by moving your mouse or finger.',
        tags: ['Interactive', 'Visual', 'Creative'],
        url: '/particle-trail',
        bgClass: 'particle-bg',
        category: 'other',
        seoTitle: 'Particle Trail - Interactive Visual Effects and Animation',
        seoDescription: 'Create stunning particle trails and visual effects with interactive mouse or touch controls.'
    },
    'typing-machine': {
        name: 'Typing Machine',
        icon: '⌨️',
        rating: '4.4',
        difficulty: '⭐⭐',
        description: 'Improve your typing speed and accuracy with this interactive typing trainer.',
        tags: ['Typing', 'Training', 'Skills'],
        url: '/typing-machine',
        bgClass: 'typing-bg',
        category: 'other',
        seoTitle: 'Typing Speed Test - Improve Your Typing Skills Online',
        seoDescription: 'Test and improve your typing speed and accuracy with our interactive typing trainer and speed test.'
    },
    'virtual-pet': {
        name: 'Virtual Pet',
        icon: '🐱',
        rating: '4.5',
        difficulty: '⭐⭐',
        description: 'Take care of your virtual pet with feeding, playing, and nurturing activities.',
        tags: ['Pet Care', 'Virtual', 'Nurturing'],
        url: '/virtual-pet',
        bgClass: 'pet-bg',
        category: 'other',
        seoTitle: 'Virtual Pet Game - Digital Pet Care and Nurturing Simulation',
        seoDescription: 'Care for your virtual pet with feeding, playing, and nurturing activities in this heartwarming simulation.'
    }
};

// Optimized Honeycomb Layout Generator - Maximum Width, Minimum Height
function generateHoneycombLayout(games, maxPerRow = 6) {
    if (games.length === 0) return [];

    const totalGames = games.length;

    // Generate layout with maximum width, minimum height principle
    function generateOptimalLayout(total, maxRow) {
        const layouts = [];

        // Try different approaches starting with maximum width
        for (let targetRows = Math.ceil(total / maxRow); targetRows <= total; targetRows++) {
            const layout = [];
            let remaining = total;
            let isOffset = false;

            for (let row = 0; row < targetRows && remaining > 0; row++) {
                let rowSize;

                if (row === targetRows - 1) {
                    // Last row - use all remaining games
                    rowSize = remaining;
                } else {
                    // Calculate optimal size for this row
                    const avgPerRow = remaining / (targetRows - row);
                    rowSize = Math.min(maxRow, Math.max(1, Math.round(avgPerRow)));

                    // Ensure odd difference with previous row if exists
                    if (layout.length > 0) {
                        const prevSize = layout[layout.length - 1].size;
                        const diff = Math.abs(rowSize - prevSize);

                        if (diff % 2 === 0) {
                            // Adjust to make odd difference
                            if (rowSize > 1 && rowSize - 1 <= remaining) {
                                rowSize = rowSize - 1;
                            } else if (rowSize < maxRow && rowSize + 1 <= remaining) {
                                rowSize = rowSize + 1;
                            }
                        }
                    }

                    // Ensure we don't exceed remaining games
                    rowSize = Math.min(rowSize, remaining);
                }

                layout.push({ size: rowSize, isOffset });
                remaining -= rowSize;
                isOffset = !isOffset;
            }

            // Validate layout - all games placed and odd differences
            if (remaining === 0) {
                let validLayout = true;
                for (let i = 0; i < layout.length - 1; i++) {
                    if (Math.abs(layout[i].size - layout[i + 1].size) % 2 === 0) {
                        validLayout = false;
                        break;
                    }
                }

                if (validLayout) {
                    // Score: prefer fewer rows, then more balanced distribution
                    const avgSize = total / layout.length;
                    const variance = layout.reduce((sum, row) =>
                        sum + Math.pow(row.size - avgSize, 2), 0) / layout.length;

                    layouts.push({
                        layout,
                        rows: layout.length,
                        variance,
                        score: layout.length * 1000 + variance // Prioritize fewer rows
                    });
                }
            }
        }

        // Return layout with minimum rows (lowest score)
        layouts.sort((a, b) => a.score - b.score);
        return layouts[0]?.layout || [{ size: total, isOffset: false }];
    }

    const optimalLayout = generateOptimalLayout(totalGames, maxPerRow);

    // Convert to game rows
    const rows = [];
    let gameIndex = 0;

    for (const rowInfo of optimalLayout) {
        const rowGames = games.slice(gameIndex, gameIndex + rowInfo.size);
        rows.push({
            games: rowGames,
            isOffset: rowInfo.isOffset
        });
        gameIndex += rowInfo.size;
    }

    return rows;
}

// Get games by category
function getGamesByCategory(category) {
    return Object.values(completeGameData).filter(game => game.category === category);
}

// Get games excluding specific category
function getGamesExcludingCategory(excludeCategory) {
    return Object.values(completeGameData).filter(game => game.category !== excludeCategory);
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        gameCategories,
        completeGameData,
        generateHoneycombLayout,
        getGamesByCategory,
        getGamesExcludingCategory
    };
}
