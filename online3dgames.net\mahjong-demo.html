<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mahjong Tiles CSS Demo</title>
    <style>
        body {
            background: linear-gradient(135deg, #1a4d2e 0%, #2d5a3d 100%);
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .demo-title {
            color: white;
            text-align: center;
            font-size: 2rem;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .tiles-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
            gap: 8px;
            margin-bottom: 40px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            max-height: 400px;
            overflow-y: auto;
        }

        .mahjong-tile {
            width: 50px;
            height: 70px;
            background: #fefefe;
            border: 1px solid #ccc;
            border-radius: 8px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow:
                2px 2px 4px rgba(0,0,0,0.3),
                inset 0 0 0 1px rgba(255,255,255,0.8);
            transform-style: preserve-3d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 42px;
            line-height: 1;
            color: #333;
            overflow: hidden;
        }

        .mahjong-tile::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: -2px;
            bottom: -2px;
            background: transparent;
            border-radius: 8px;
            z-index: -1;
        }

        .mahjong-tile::after {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            right: 3px;
            bottom: 3px;
            border: 1px solid #f0f0f0;
            border-radius: 6px;
            opacity: 0.6;
            z-index: 1;
            pointer-events: none;
        }

        .mahjong-tile:hover {
            transform: translateY(-1px) scale(1.02);
            box-shadow:
                3px 3px 6px rgba(0,0,0,0.4),
                inset 0 0 0 1px rgba(255,255,255,0.9);
        }

        .mahjong-tile.selected {
            background: #fff8dc;
            border-color: #daa520;
            transform: translateY(-2px) scale(1.03);
            box-shadow:
                0 4px 8px rgba(218, 165, 32, 0.6),
                inset 0 0 0 2px #daa520;
            color: #8b4513;
        }

        .mahjong-tile.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            filter: grayscale(0.7);
        }

        /* 不同类型的麻将牌样式 */
        .tile-wan {
            color: #d32f2f;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        .tile-tong {
            color: #1976d2;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        .tile-tiao {
            color: #388e3c;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        .tile-feng {
            color: #7b1fa2;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        .tile-jian {
            color: #d32f2f;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        .tile-hua {
            color: #ff6f00;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        .tile-season {
            color: #c2185b;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        /* 特殊颜色处理 */
        .tile-jian[data-value="fa"] {
            filter: hue-rotate(120deg) brightness(1.1) contrast(1.2);
        }

        /* Unicode麻将牌样式 */
        .mahjong-tile {
            font-size: 48px;
            line-height: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 特殊牌样式 */
        .tile-special {
            background: linear-gradient(145deg, #fff8dc 0%, #f0e68c 50%, #daa520 100%);
            border-color: #b8860b;
        }

        .tile-back {
            background: linear-gradient(145deg, #2d5a2d 0%, #1a4d1a 100%);
            border-color: #1a4d1a;
            color: #ffffff;
            font-size: 32px;
            filter: none;
        }

        .tile-back::before {
            background: linear-gradient(135deg, #1a4d1a 0%, #0d2d0d 100%) !important;
        }

        .layout-demo {
            margin-top: 40px;
            text-align: center;
        }

        .layout-title {
            color: white;
            font-size: 1.5rem;
            margin-bottom: 20px;
        }

        .pyramid-layout {
            display: inline-block;
            position: relative;
            padding: 20px;
        }

        .layer {
            display: flex;
            justify-content: center;
            margin-bottom: -20px;
            position: relative;
        }

        .layer-1 { z-index: 1; }
        .layer-2 { z-index: 2; margin-left: 30px; }
        .layer-3 { z-index: 3; margin-left: 60px; }

        .layer .mahjong-tile {
            margin: 0 2px;
        }

        .controls {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(145deg, #4caf50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.3);
        }

        .info {
            color: rgba(255,255,255,0.8);
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🀄 真实麻将牌样式演示 - 简洁立体设计</h1>
        
        <div class="tiles-showcase">
            <!-- 万字牌 (Characters) 1-9 -->
            <div class="mahjong-tile tile-wan" data-type="wan" data-value="1">🀇</div>
            <div class="mahjong-tile tile-wan" data-type="wan" data-value="2">🀈</div>
            <div class="mahjong-tile tile-wan" data-type="wan" data-value="3">🀉</div>
            <div class="mahjong-tile tile-wan" data-type="wan" data-value="4">🀊</div>
            <div class="mahjong-tile tile-wan" data-type="wan" data-value="5">🀋</div>
            <div class="mahjong-tile tile-wan" data-type="wan" data-value="6">🀌</div>
            <div class="mahjong-tile tile-wan" data-type="wan" data-value="7">🀍</div>
            <div class="mahjong-tile tile-wan" data-type="wan" data-value="8">🀎</div>
            <div class="mahjong-tile tile-wan" data-type="wan" data-value="9">🀏</div>

            <!-- 条字牌 (Bamboos) 1-9 -->
            <div class="mahjong-tile tile-tiao" data-type="tiao" data-value="1">🀐</div>
            <div class="mahjong-tile tile-tiao" data-type="tiao" data-value="2">🀑</div>
            <div class="mahjong-tile tile-tiao" data-type="tiao" data-value="3">🀒</div>
            <div class="mahjong-tile tile-tiao" data-type="tiao" data-value="4">🀓</div>
            <div class="mahjong-tile tile-tiao" data-type="tiao" data-value="5">🀔</div>
            <div class="mahjong-tile tile-tiao" data-type="tiao" data-value="6">🀕</div>
            <div class="mahjong-tile tile-tiao" data-type="tiao" data-value="7">🀖</div>
            <div class="mahjong-tile tile-tiao" data-type="tiao" data-value="8">🀗</div>
            <div class="mahjong-tile tile-tiao" data-type="tiao" data-value="9">🀘</div>

            <!-- 筒字牌 (Circles/Dots) 1-9 -->
            <div class="mahjong-tile tile-tong" data-type="tong" data-value="1">🀙</div>
            <div class="mahjong-tile tile-tong" data-type="tong" data-value="2">🀚</div>
            <div class="mahjong-tile tile-tong" data-type="tong" data-value="3">🀛</div>
            <div class="mahjong-tile tile-tong" data-type="tong" data-value="4">🀜</div>
            <div class="mahjong-tile tile-tong" data-type="tong" data-value="5">🀝</div>
            <div class="mahjong-tile tile-tong" data-type="tong" data-value="6">🀞</div>
            <div class="mahjong-tile tile-tong" data-type="tong" data-value="7">🀟</div>
            <div class="mahjong-tile tile-tong" data-type="tong" data-value="8">🀠</div>
            <div class="mahjong-tile tile-tong" data-type="tong" data-value="9">🀡</div>

            <!-- 风牌 (Winds) -->
            <div class="mahjong-tile tile-feng" data-type="feng" data-value="dong">🀀</div>
            <div class="mahjong-tile tile-feng" data-type="feng" data-value="nan">🀁</div>
            <div class="mahjong-tile tile-feng" data-type="feng" data-value="xi">🀂</div>
            <div class="mahjong-tile tile-feng" data-type="feng" data-value="bei">🀃</div>

            <!-- 箭牌 (Dragons) -->
            <div class="mahjong-tile tile-jian" data-type="jian" data-value="fa">🀅</div>
            <div class="mahjong-tile tile-jian" data-type="jian" data-value="bai">🀆</div>

            <!-- 花牌 (Flowers) -->
            <div class="mahjong-tile tile-hua" data-type="hua" data-value="mei">🀢</div>
            <div class="mahjong-tile tile-hua" data-type="hua" data-value="lan">🀣</div>
            <div class="mahjong-tile tile-hua" data-type="hua" data-value="zhu">🀤</div>
            <div class="mahjong-tile tile-hua" data-type="hua" data-value="ju">🀥</div>

            <!-- 季节牌 (Seasons) -->
            <div class="mahjong-tile tile-season" data-type="season" data-value="chun">🀦</div>
            <div class="mahjong-tile tile-season" data-type="season" data-value="xia">🀧</div>
            <div class="mahjong-tile tile-season" data-type="season" data-value="qiu">🀨</div>
            <div class="mahjong-tile tile-season" data-type="season" data-value="dong">🀩</div>

            <!-- 特殊牌 -->
            <div class="mahjong-tile tile-special" data-type="special" data-value="joker">🀪</div>
            <div class="mahjong-tile tile-back" data-type="back" data-value="back">🀫</div>
        </div>

        <div class="layout-demo">
            <h2 class="layout-title">经典金字塔布局示例</h2>
            <div class="pyramid-layout">
                <div class="layer layer-3">
                    <div class="mahjong-tile tile-wan">🀇</div>
                    <div class="mahjong-tile tile-tong">🀙</div>
                </div>
                <div class="layer layer-2">
                    <div class="mahjong-tile tile-tiao">🀐</div>
                    <div class="mahjong-tile tile-feng">🀀</div>
                    <div class="mahjong-tile tile-wan">🀈</div>
                </div>
                <div class="layer layer-1">
                    <div class="mahjong-tile tile-tong">🀚</div>
                    <div class="mahjong-tile tile-tiao">🀑</div>
                    <div class="mahjong-tile tile-feng">🀁</div>
                    <div class="mahjong-tile tile-jian">🀅</div>
                    <div class="mahjong-tile tile-wan">🀉</div>
                    <div class="mahjong-tile tile-hua">🀦</div>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="btn" onclick="selectRandomTile()">随机选择</button>
            <button class="btn" onclick="resetSelection()">重置选择</button>
            <button class="btn" onclick="toggleDisabled()">切换禁用</button>
        </div>

        <div class="info">
            点击麻将牌查看选中效果 | 悬停查看3D效果 | 完整144张Unicode麻将牌 | 简洁真实的立体设计
        </div>
    </div>

    <script>
        let selectedTiles = [];

        document.querySelectorAll('.mahjong-tile').forEach(tile => {
            tile.addEventListener('click', function() {
                if (this.classList.contains('disabled')) return;
                
                if (this.classList.contains('selected')) {
                    this.classList.remove('selected');
                    selectedTiles = selectedTiles.filter(t => t !== this);
                } else {
                    this.classList.add('selected');
                    selectedTiles.push(this);
                }
            });
        });

        function selectRandomTile() {
            resetSelection();
            const tiles = document.querySelectorAll('.mahjong-tile:not(.disabled)');
            const randomTile = tiles[Math.floor(Math.random() * tiles.length)];
            randomTile.classList.add('selected');
            selectedTiles = [randomTile];
        }

        function resetSelection() {
            selectedTiles.forEach(tile => tile.classList.remove('selected'));
            selectedTiles = [];
        }

        function toggleDisabled() {
            const tiles = document.querySelectorAll('.mahjong-tile');
            tiles.forEach((tile, index) => {
                if (index % 3 === 0) {
                    tile.classList.toggle('disabled');
                }
            });
        }
    </script>
</body>
</html>
