
let sudokuGame;
let sudokuGenerator;
let sudokuSolver;

window.gameInfo = function(message, title = 'Hint') {
    return gameAlert(message, title);
};

document.addEventListener('DOMContentLoaded', function() {
    sudokuGame = new SudokuGame();
    sudokuGenerator = new SudokuGenerator();
    sudokuSolver = new SudokuSolver();

    setupEventListeners();

    setupModalClickToClose();

    const hasAutoSave = localStorage.getItem('sudoku-save');
    if (hasAutoSave) {
        showLoadOption();
    }
});

/**
 * Setup modal click to close functionality
 */
function setupModalClickToClose() {
    const overlays = [
        'game-start',
        'difficulty-modal',
        'game-complete',
        'game-failed'
    ];

    overlays.forEach(overlayId => {
        const overlay = document.getElementById(overlayId);
        if (overlay) {
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    closeModal(overlayId);
                }
            });
        }
    });
}

/**
 * Close a specific modal
 */
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;

    switch (modalId) {
        case 'game-start':
            break;
        case 'difficulty-modal':
            closeDifficultyModal();
            break;
        case 'game-complete':
            modal.classList.add('hidden');
            break;
        case 'game-failed':
            closeFailedDialog();
            break;
        default:
            modal.classList.add('hidden');
            break;
    }
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    document.getElementById('start-btn').addEventListener('click', showDifficultyModal);

    document.getElementById('new-game-btn').addEventListener('click', showNewGameDialog);
    document.getElementById('hint-btn').addEventListener('click', getHint);
    document.getElementById('solve-btn').addEventListener('click', autoSolve);
    document.getElementById('check-btn').addEventListener('click', checkBoard);
    
    document.querySelectorAll('.difficulty-option').forEach(option => {
        option.addEventListener('click', function() {
            selectDifficultyAndStart(this.dataset.level);
        });
    });

    document.getElementById('cancel-difficulty-btn').addEventListener('click', closeDifficultyModal);
    
    document.getElementById('undo-btn').addEventListener('click', () => sudokuGame.undo());
    document.getElementById('redo-btn').addEventListener('click', () => sudokuGame.redo());
    document.getElementById('clear-btn').addEventListener('click', clearBoard);
    document.getElementById('save-btn').addEventListener('click', () => sudokuGame.saveGame());
    document.getElementById('load-btn').addEventListener('click', () => sudokuGame.loadGame());
    
    document.getElementById('new-game-complete-btn').addEventListener('click', restartCurrentDifficulty);
    document.getElementById('change-difficulty-complete-btn').addEventListener('click', () => {
        document.getElementById('game-complete').classList.add('hidden');
        showDifficultyModal();
    });
    document.getElementById('share-btn').addEventListener('click', shareScore);

    document.getElementById('retry-btn').addEventListener('click', restartCurrentDifficulty);
    document.getElementById('change-difficulty-btn').addEventListener('click', () => {
        closeFailedDialog();
        showDifficultyModal();
    });
    document.getElementById('close-failed-btn').addEventListener('click', closeFailedDialog);
}

/**
 * Show difficulty selection modal
 */
function showDifficultyModal() {
    document.getElementById('game-start').classList.add('hidden');
    document.getElementById('difficulty-modal').classList.remove('hidden');
}

/**
 * Close difficulty selection modal
 */
function closeDifficultyModal() {
    document.getElementById('difficulty-modal').classList.add('hidden');
    document.getElementById('game-start').classList.remove('hidden');
}

/**
 * Select difficulty and start game
 */
function selectDifficultyAndStart(level) {
    document.getElementById('difficulty-modal').classList.add('hidden');
    startNewGameWithDifficulty(level);
}

/**
 * Start new game (with specified difficulty)
 */
function startNewGameWithDifficulty(difficulty = 'easy') {
    const { puzzle, solution } = sudokuGenerator.generatePuzzle(difficulty);

    sudokuGame.board = puzzle.map(row => [...row]);
    sudokuGame.initialBoard = puzzle.map(row => [...row]);
    sudokuGame.solution = solution.map(row => [...row]);

    sudokuGame.updateBoard();

    sudokuGame.startGame(difficulty);

    document.getElementById('game-start').classList.add('hidden');
    document.getElementById('game-complete').classList.add('hidden');
    document.getElementById('game-failed').classList.add('hidden');
    document.getElementById('difficulty-modal').classList.add('hidden');
}

/**
 * Restart current difficulty
 */
function restartCurrentDifficulty() {
    const currentDifficulty = sudokuGame.difficulty || 'easy';
    startNewGameWithDifficulty(currentDifficulty);
}

/**
 * Start new game (compatible with old version)
 */
function startNewGame() {
    showDifficultyModal();
}

/**
 * Show new game dialog
 */
function showNewGameDialog() {
    gameConfirm('Are you sure you want to start a new game? The current progress will be lost.', 'New Game').then(result => {
        if (result) {
            showDifficultyModal();
        }
    });
}

/**
 * Select difficulty
 */
function selectDifficulty(level) {
    document.querySelectorAll('.difficulty-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-level="${level}"]`).classList.add('active');

    showDifficultyPreview(level);
}

/**
 * Show difficulty preview
 */
function showDifficultyPreview(level) {
    const difficultyInfo = {
        'easy': { name: 'Easy', holes: '35-40 empty spaces', time: '5-10 minutes', stars: '⭐⭐' },
        'medium': { name: 'Medium', holes: '45-50 empty spaces', time: '10-20 minutes', stars: '⭐⭐⭐' },
        'hard': { name: 'Hard', holes: '55-60 empty spaces', time: '20-40 minutes', stars: '⭐⭐⭐⭐' },
        'expert': { name: 'Expert', holes: '65-70 empty spaces', time: '40 minutes or more', stars: '⭐⭐⭐⭐⭐' }
    };

    const info = difficultyInfo[level];
    if (info) {
        document.getElementById('difficulty').textContent = info.name;

        // Update button text to show more information
        const button = document.querySelector(`[data-level="${level}"]`);
        if (button) {
            const originalText = button.textContent;
            button.innerHTML = `${originalText}<br><small style="font-size: 0.8em; opacity: 0.8;">${info.holes}</small>`;

            // Restore original text after 3 seconds
            setTimeout(() => {
                button.innerHTML = originalText;
            }, 3000);
        }
    }
}

/**
 * Check if the game is failed
 */
function checkGameFailed() {
    try {
        // Check if there are empty spaces
        const emptyCount = sudokuGame.board.flat().filter(cell => cell === 0).length;
        if (emptyCount === 0) {
            return false; // No empty spaces, not considered failed
        }

        // Only consider failure when there are obvious conflicts
        const hasConflicts = !sudokuGame.checkConflicts();
        if (hasConflicts) {
            return true;
        }

        // For cases with many empty spaces (at the beginning of the game), do not check for failure
        if (emptyCount > 50) {
            return false;
        }

        // Only perform deep checks when the game has progressed to a certain extent
        return false; // Simplify failure checks to avoid false positives

    } catch (error) {
        console.error('Error checking game failure state:', error);
        return false; // Assume game can continue if there's an error
    }
}

/**
 * Show game failed
 */
function showGameFailed() {
    // Do not set gameCompleted to true, allowing the player to continue trying
    // sudokuGame.gameCompleted = true;
    // sudokuGame.stopTimer();

    // Show failure interface
    document.getElementById('game-failed').classList.remove('hidden');
}

/**
 * Close failure dialog
 */
function closeFailedDialog() {
    document.getElementById('game-failed').classList.add('hidden');
}

/**
 * Get hint
 */
function getHint() {
    if (!sudokuGame.gameStarted || sudokuGame.gameCompleted) {
        gameWarning('Please start the game first', 'Hint');
        return;
    }

    // Check if the game is completed
    if (sudokuGame.isCompleted()) {
        gameAlert('Sudoku is completed!', 'Hint');
        return;
    }

    // Check if there are empty spaces
    const emptyCount = sudokuGame.board.flat().filter(cell => cell === 0).length;
    if (emptyCount === 0) {
        gameError('Sudoku is filled but has errors, please check the red marked cells', 'Cannot hint');
        return;
    }

    try {
        // Get hint directly, without too much pre-checking
        const hint = sudokuSolver.getHint(sudokuGame.board);

        if (!hint) {
            // Check if there are obvious conflicts
            const hasConflicts = !sudokuGame.checkConflicts();
            if (hasConflicts) {
                gameError('The current board has conflicts, please correct the red marked cells', 'Cannot hint');
            } else {
                gameError('Cannot get hint, possibly the current state is unsolvable', 'Hint failed');
            }
            return;
        }

        // Save state
        sudokuGame.saveState();

        // Apply hint
        sudokuGame.board[hint.row][hint.col] = hint.number;
        sudokuGame.updateCellDisplay(hint.row, hint.col);

        // Add hint style
        sudokuGame.cells[hint.row][hint.col].classList.add('hint');
        setTimeout(() => {
            sudokuGame.cells[hint.row][hint.col].classList.remove('hint');
        }, 1000);

        // Select the cell (focus moves to the hint position)
        sudokuGame.selectCell(hint.row, hint.col);

        // Update progress
        sudokuGame.updateProgress();

        // Check conflicts
        sudokuGame.checkConflicts();

        // Check if completed
        if (sudokuGame.isCompleted()) {
            setTimeout(() => {
                sudokuGame.completeGame();
            }, 500);
        }

    } catch (error) {
        console.error('Error getting hint:', error);
        gameError('Error getting hint, please try again', 'Hint failed');
    }
}

/**
 * Auto solve
 */
function autoSolve() {
    if (!sudokuGame.gameStarted || sudokuGame.gameCompleted) {
        gameWarning('Please start the game first', 'Auto solve');
        return;
    }

    gameConfirm('Are you sure you want to auto solve? This will complete the entire Sudoku.', 'Auto solve').then(result => {
        if (!result) return;

        // Use setTimeout to update the UI and show loading state
        setTimeout(() => {
            try {
                // Check if the current board has errors
                if (sudokuSolver.hasErrors(sudokuGame.board)) {
                    gameError('The current board has errors, cannot be solved. Please correct the red marked cells.', 'Solve failed');
                    return;
                }

                // Create a copy of the board for solving
                const boardCopy = sudokuGame.board.map(row => [...row]);

                // Show solving progress
                gameAlert('Solving...', 'Auto solve');

                // Use setTimeout again to ensure the progress prompt is displayed
                setTimeout(() => {
                    const solution = sudokuSolver.solve(boardCopy);

                    if (!solution) {
                        gameError('Cannot solve the current Sudoku, possibly there are errors, no solution, or solving timeout', 'Solve failed');
                        return;
                    }

                    // Save state
                    sudokuGame.saveState();

                    // Apply solution
                    sudokuGame.board = solution;
                    sudokuGame.updateBoard();

                    // Complete game
                    sudokuGame.completeGame();

                    gameSuccess('Sudoku has been automatically completed!', 'Solve successfully');

                }, 200);

            } catch (error) {
                console.error('Error during solving:', error);
                gameError('Error during solving, please try again', 'Solve failed');
            }
        }, 100);
    });
}

/**
 * Check board
 */
function checkBoard() {
    if (!sudokuGame.gameStarted || sudokuGame.gameCompleted) {
        gameWarning('Please start the game first', 'Check');
        return;
    }
    
    sudokuGame.checkBoard();
}

/**
 * Clear board
 */
function clearBoard() {
    if (!sudokuGame.gameStarted || sudokuGame.gameCompleted) {
        gameWarning('Please start the game first', 'Clear');
        return;
    }
    
    gameConfirm('Are you sure you want to clear all filled numbers?', 'Clear confirmation').then(result => {
        if (result) {
            sudokuGame.clearBoard();
            gameSuccess('Board has been cleared', 'Clear completed');
        }
    });
}

/**
 * Share score
 */
function shareScore() {
    const time = document.getElementById('final-time').textContent;
    const errors = document.getElementById('final-errors').textContent;
    const difficulty = document.getElementById('final-difficulty').textContent;
    
    const shareText = `I completed the ${difficulty} difficulty challenge in the Sudoku game!\nTime: ${time}\nErrors: ${errors}\nCome and challenge!`;
    
    if (navigator.share) {
        navigator.share({
            title: 'Sudoku game score',
            text: shareText,
            url: window.location.href
        }).catch(err => {
            console.log('Share failed:', err);
            copyToClipboard(shareText);
        });
    } else {
        copyToClipboard(shareText);
    }
}

/**
 * Copy to clipboard
 */
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            gameSuccess('Score has been copied to clipboard!', 'Share successfully');
        }).catch(() => {
            fallbackCopyToClipboard(text);
        });
    } else {
        fallbackCopyToClipboard(text);
    }
}

/**
 * Fallback copy method
 */
function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    
    try {
        document.execCommand('copy');
        gameSuccess('Score has been copied to clipboard!', 'Share successfully');
    } catch (err) {
        gameError('Copy failed, please copy manually', 'Share failed');
    }
    
    document.body.removeChild(textArea);
}

/**
 * Show load option
 */
function showLoadOption() {
    gameConfirm('Found saved game progress, do you want to continue the last game?', 'Continue game').then(result => {
        if (result) {
            sudokuGame.loadGame();
        }
    });
}

/**
 * Auto save when page is unloaded
 */
window.addEventListener('beforeunload', function() {
    if (sudokuGame && sudokuGame.gameStarted && !sudokuGame.gameCompleted) {
        sudokuGame.saveGame();
    }
});

/**
 * Keyboard shortcuts
 */
document.addEventListener('keydown', function(e) {
    // ESC key closes the modal
    if (e.key === 'Escape') {
        e.preventDefault();
        closeTopModal();
        return;
    }

    if (!sudokuGame || !sudokuGame.gameStarted || sudokuGame.gameCompleted) return;

    // Ctrl/Cmd + Z: Undo
    if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        sudokuGame.undo();
    }
    // Ctrl/Cmd + Shift + Z or Ctrl/Cmd + Y: Redo
    else if ((e.ctrlKey || e.metaKey) && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {
        e.preventDefault();
        sudokuGame.redo();
    }
    // Ctrl/Cmd + S: Save
    else if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        sudokuGame.saveGame();
    }
    // H: Hint
    else if (e.key === 'h' || e.key === 'H') {
        e.preventDefault();
        getHint();
    }
    // C: Check
    else if (e.key === 'c' || e.key === 'C') {
        e.preventDefault();
        checkBoard();
    }
});

/**
 * Close the topmost modal
 */
function closeTopModal() {
    const modals = [
        'difficulty-modal',
        'game-failed',
        'game-complete',
        'game-start'
    ];

    // Close modals by priority
    for (const modalId of modals) {
        const modal = document.getElementById(modalId);
        if (modal && !modal.classList.contains('hidden')) {
            closeModal(modalId);
            break;
        }
    }
}

// Export functions for use by other modules
window.sudokuGame = sudokuGame;
window.startNewGame = startNewGame;
