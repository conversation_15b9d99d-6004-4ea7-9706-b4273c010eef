/* Mahjong Connect - Main Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1a4d2e 0%, #2d5a3d 100%);
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a4d2e 0%, #2d5a3d 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    max-width: 400px;
}

.loader {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading {
    color: white;
    font-size: 18px;
    font-weight: 500;
}

/* Game Container */
.game-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Game Header */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.header-left h1 {
    font-size: 2rem;
    margin: 0 0 10px 0;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.game-stats {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.game-stats span {
    background: rgba(255,255,255,0.2);
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    border: 1px solid rgba(255,255,255,0.3);
}

.header-right {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: linear-gradient(145deg, #4caf50 0%, #45a049 100%);
    border-color: #45a049;
}

.btn-secondary {
    background: linear-gradient(145deg, #757575 0%, #616161 100%);
    border-color: #616161;
}

.btn-hint {
    background: linear-gradient(145deg, #ff9800 0%, #f57c00 100%);
    border-color: #f57c00;
}

.btn-undo {
    background: linear-gradient(145deg, #2196f3 0%, #1976d2 100%);
    border-color: #1976d2;
}

.btn-restart {
    background: linear-gradient(145deg, #f44336 0%, #d32f2f 100%);
    border-color: #d32f2f;
}

.btn-rotate {
    background: linear-gradient(145deg, #9c27b0 0%, #7b1fa2 100%);
    border-color: #7b1fa2;
}

/* Game Board */
.game-board {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 500px;
    padding: 30px 20px 40px 20px;
    overflow: hidden;
}

.mahjong-grid {
    position: relative;
    margin: 0 auto;
    width: 100%;
    max-width: 800px;
    min-height: 500px;
    height: 100%;
    perspective: 1000px;
    perspective-origin: center center;
    transform-style: preserve-3d;
    transition: transform 0.5s ease;
}

.mahjong-layout {
    position: relative;
    margin: 0 auto;
    width: 100%;
    max-width: 800px;
}

/* Game Messages */
.game-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.message-content {
    background: linear-gradient(145deg, #2d5a3d 0%, #1a4d2e 100%);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 500px;
    border: 2px solid rgba(255,255,255,0.2);
    box-shadow: 0 20px 40px rgba(0,0,0,0.5);
}

.message-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #ffffff;
}

.message-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: rgba(255,255,255,0.9);
}

.message-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: linear-gradient(145deg, #2d5a3d 0%, #1a4d2e 100%);
    border-radius: 15px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    border: 2px solid rgba(255,255,255,0.2);
    box-shadow: 0 20px 40px rgba(0,0,0,0.5);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.modal-header h3 {
    font-size: 1.5rem;
    color: #ffffff;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(255,255,255,0.2);
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid rgba(255,255,255,0.2);
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

/* Settings */
.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #ffffff;
}

.setting-group select,
.setting-group input[type="checkbox"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 8px;
    background: rgba(255,255,255,0.1);
    color: white;
    font-size: 14px;
}

.setting-group input[type="checkbox"] {
    width: auto;
    transform: scale(1.2);
}

/* Rules Content */
.rules-content h4 {
    color: #ffffff;
    margin: 20px 0 10px 0;
    font-size: 1.2rem;
}

.rules-content p,
.rules-content li {
    color: rgba(255,255,255,0.9);
    margin-bottom: 8px;
    line-height: 1.6;
}

.rules-content ul {
    margin-left: 20px;
    margin-bottom: 20px;
}

/* SEO Content */
.seo-content-section {
    background: rgba(255,255,255,0.05);
    padding: 60px 0;
    margin-top: 40px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.game-description h2,
.game-description h3 {
    color: #ffffff;
    margin-bottom: 20px;
}

.game-description p,
.game-description li {
    color: rgba(255,255,255,0.9);
    margin-bottom: 15px;
    line-height: 1.7;
}

.game-description ul {
    margin-left: 20px;
    margin-bottom: 25px;
}

/* Game Recommendations */
.game-recommendations {
    padding: 60px 0;
    background: rgba(0,0,0,0.2);
}

.game-recommendations h2 {
    text-align: center;
    color: #ffffff;
    margin-bottom: 40px;
    font-size: 2rem;
}

.recommendations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

.recommendation-card {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    border: 1px solid rgba(255,255,255,0.2);
}

.recommendation-card:hover {
    transform: translateY(-5px);
    background: rgba(255,255,255,0.2);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.recommendation-card .game-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.recommendation-card .game-name {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: #ffffff;
}

.recommendation-card .game-rating {
    color: #ffd700;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        font-size: 14px;
    }

    .game-container {
        padding: 10px;
    }

    .game-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
        padding: 10px 15px;
    }

    .header-left h1 {
        font-size: 1.5rem;
        margin-bottom: 8px;
    }

    .game-stats {
        justify-content: center;
        gap: 8px;
        flex-wrap: wrap;
    }

    .game-stats span {
        font-size: 11px;
        padding: 3px 8px;
        min-width: 60px;
        text-align: center;
    }

    .header-right {
        justify-content: center;
        gap: 6px;
        flex-wrap: wrap;
    }

    .btn {
        padding: 5px 10px;
        font-size: 11px;
        min-width: 32px;
        height: 32px;
        border-radius: 6px;
    }

    .game-board {
        padding: 10px 5px 20px 5px;
        min-height: 350px;
    }

    .mahjong-grid {
        transform: scale(0.8);
        transform-origin: center;
    }

    .mahjong-layout {
        transform: scale(0.8);
        transform-origin: center;
    }

    .message-content {
        padding: 25px 15px;
        margin: 15px;
        border-radius: 15px;
    }

    .message-content h2 {
        font-size: 1.8rem;
        margin-bottom: 15px;
    }

    .message-content p {
        font-size: 1rem;
        margin-bottom: 20px;
    }

    .modal-content {
        margin: 15px;
        width: calc(100% - 30px);
        border-radius: 12px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 15px;
    }

    .modal-header h3 {
        font-size: 1.3rem;
    }

    .setting-group {
        margin-bottom: 15px;
    }

    .setting-group label {
        font-size: 14px;
        margin-bottom: 6px;
    }

    .setting-group select {
        padding: 6px 10px;
        font-size: 13px;
    }

    .recommendations-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .recommendation-card {
        padding: 20px;
    }

    .recommendation-card .game-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }

    .recommendation-card .game-name {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    body {
        font-size: 13px;
    }

    .game-container {
        padding: 5px;
    }

    .game-header {
        padding: 8px 10px;
        gap: 10px;
    }

    .header-left h1 {
        font-size: 1.3rem;
        margin-bottom: 5px;
    }

    .game-stats {
        flex-direction: column;
        gap: 5px;
        width: 100%;
    }

    .game-stats span {
        font-size: 10px;
        padding: 2px 6px;
        width: 100%;
        max-width: 120px;
        margin: 0 auto;
    }

    .header-right {
        flex-wrap: wrap;
        gap: 4px;
        justify-content: center;
    }

    .btn {
        min-width: 28px;
        height: 28px;
        padding: 3px 6px;
        font-size: 10px;
        border-radius: 4px;
    }

    .game-board {
        padding: 5px 2px 15px 2px;
        min-height: 300px;
    }

    .mahjong-grid {
        transform: scale(0.65);
        transform-origin: center;
    }

    .mahjong-layout {
        transform: scale(0.65);
        transform-origin: center;
    }

    .message-content {
        padding: 20px 12px;
        margin: 10px;
    }

    .message-content h2 {
        font-size: 1.4rem;
        margin-bottom: 12px;
    }

    .message-content p {
        font-size: 0.9rem;
        margin-bottom: 15px;
        line-height: 1.4;
    }

    .message-buttons {
        flex-direction: column;
        gap: 8px;
    }

    .message-buttons .btn {
        width: 100%;
        height: 36px;
        font-size: 12px;
    }

    .modal-content {
        margin: 10px;
        width: calc(100% - 20px);
        max-height: 85vh;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 12px;
    }

    .modal-header h3 {
        font-size: 1.2rem;
    }

    .setting-group {
        margin-bottom: 12px;
    }

    .setting-group label {
        font-size: 13px;
        margin-bottom: 4px;
    }

    .setting-group select {
        padding: 5px 8px;
        font-size: 12px;
    }

    .rules-content h4 {
        font-size: 1.1rem;
        margin: 15px 0 8px 0;
    }

    .rules-content p,
    .rules-content li {
        font-size: 13px;
        line-height: 1.5;
    }

    .seo-content-section {
        padding: 40px 0;
    }

    .game-description h2,
    .game-description h3 {
        font-size: 1.3rem;
        margin-bottom: 15px;
    }

    .game-description p,
    .game-description li {
        font-size: 13px;
        line-height: 1.6;
    }

    .game-recommendations {
        padding: 40px 0;
    }

    .game-recommendations h2 {
        font-size: 1.5rem;
        margin-bottom: 30px;
    }

    .recommendation-card {
        padding: 15px;
    }

    .recommendation-card .game-icon {
        font-size: 2rem;
        margin-bottom: 8px;
    }

    .recommendation-card .game-name {
        font-size: 1rem;
        margin-bottom: 5px;
    }
}

/* Touch-specific optimizations */
@media (hover: none) and (pointer: coarse) {
    .btn:hover {
        transform: none;
        background: rgba(255,255,255,0.2);
    }

    .btn:active {
        transform: scale(0.95);
        background: rgba(255,255,255,0.4);
    }

    .mahjong-tile:hover {
        transform: none;
        box-shadow:
            2px 2px 4px rgba(0,0,0,0.3),
            inset 0 0 0 1px rgba(255,255,255,0.8);
    }

    .mahjong-tile:active {
        transform: scale(0.95);
    }

    .mahjong-tile.disabled:active {
        transform: scale(0.95) !important;
    }

    .recommendation-card:hover {
        transform: none;
    }

    .recommendation-card:active {
        transform: scale(0.98);
    }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .game-header {
        flex-direction: row;
        padding: 8px 15px;
    }

    .header-left h1 {
        font-size: 1.2rem;
        margin-bottom: 0;
    }

    .game-stats {
        flex-direction: row;
        gap: 8px;
    }

    .game-stats span {
        font-size: 10px;
        padding: 2px 6px;
        width: auto;
    }

    .game-board {
        min-height: 250px;
    }

    .mahjong-grid {
        transform: scale(0.7);
    }

    .mahjong-layout {
        transform: scale(0.7);
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .btn {
        border-width: 0.5px;
    }

    .game-header {
        border-width: 0.5px;
    }

    .modal-content {
        border-width: 1px;
    }
}
