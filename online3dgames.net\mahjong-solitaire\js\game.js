/**
 * Mahjong Connect Game Engine
 */

class MahjongConnectGame {
    constructor() {
        this.tileSet = new MahjongTileSet();
        this.selectedTiles = [];
        this.score = 0;
        this.startTime = null;
        this.gameTime = 0;
        this.gameTimer = null;
        this.isGameActive = false;
        this.hintsUsed = 0;
        this.tileWidth = 50;
        this.tileHeight = 70;
        this.tileSpacing = 5;
        this.layerHeight = 8; // 3D layer separation
        this.currentRotation = 0; // 0=front, 90=right, 180=back, 270=left
        
        this.initializeGame();
        this.setupEventListeners();
    }

    /**
     * Initialize the game
     */
    initializeGame() {
        this.gameBoard = document.getElementById('mahjongGrid');

        // Give DOM time to render before starting game
        setTimeout(() => {
            this.hideLoadingScreen();
            this.newGame();
        }, 100);
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Game buttons
        document.getElementById('rotateLeftBtn').addEventListener('click', () => this.rotateLeft());
        document.getElementById('rotateRightBtn').addEventListener('click', () => this.rotateRight());
        document.getElementById('restartBtn').addEventListener('click', () => this.newGame());
        document.getElementById('hintBtn').addEventListener('click', () => this.showHint());
        document.getElementById('rulesBtn').addEventListener('click', () => this.showRules());
        document.getElementById('fullscreenBtn').addEventListener('click', () => this.toggleFullscreen());

        // Modal buttons
        document.getElementById('closeRulesBtn').addEventListener('click', () => this.hideRules());
        document.getElementById('closeRulesOkBtn').addEventListener('click', () => this.hideRules());
        document.getElementById('newGameBtn').addEventListener('click', () => this.newGame());
        document.getElementById('closeMessageBtn').addEventListener('click', () => this.hideGameMessage());

        // Tile click events will be added dynamically
    }

    /**
     * Start a new game
     */
    newGame() {
        this.hideGameMessage();
        this.clearGame();
        this.generateTiles();
        this.renderTiles();
        this.clearTileStates();
        this.startTimer();
        this.isGameActive = true;
        this.updateGameStats();
    }

    /**
     * Clear current game state
     */
    clearGame() {
        this.selectedTiles = [];
        this.score = 0;
        this.hintsUsed = 0;
        this.gameTime = 0;
        
        if (this.gameTimer) {
            clearInterval(this.gameTimer);
            this.gameTimer = null;
        }
        
        // Clear game board
        if (this.gameBoard) {
            this.gameBoard.innerHTML = '';
        }
    }

    /**
     * Generate tiles for the game
     */
    generateTiles() {
        this.tileSet.generateTiles();
    }

    /**
     * Render tiles on the game board
     */
    renderTiles() {
        if (!this.gameBoard) return;

        const boardRect = this.gameBoard.getBoundingClientRect();
        let boardWidth = boardRect.width;
        let boardHeight = boardRect.height;

        // Fallback dimensions if board is not yet rendered
        if (boardWidth === 0 || boardHeight === 0) {
            boardWidth = 800;
            boardHeight = 500;
        }

        // Set 3D perspective on the game board
        this.gameBoard.style.perspective = '1000px';
        this.gameBoard.style.perspectiveOrigin = 'center center';
        this.gameBoard.style.transformStyle = 'preserve-3d';
        this.gameBoard.style.transform = `rotateX(-20deg) rotateY(${this.currentRotation}deg)`;

        // Calculate grid dimensions
        const cols = this.tileSet.gridCols;
        const rows = this.tileSet.gridRows;

        // Calculate tile positions
        const totalTileWidth = cols * this.tileWidth + (cols - 1) * this.tileSpacing;
        const totalTileHeight = rows * this.tileHeight + (rows - 1) * this.tileSpacing;

        const startX = (boardWidth - totalTileWidth) / 2;
        const startY = (boardHeight - totalTileHeight) / 2;

        // Update blocked status before rendering
        this.tileSet.updateBlockedStatus();

        this.tileSet.tiles.forEach(tile => {
            if (!tile.removed) {
                const x = startX + tile.col * (this.tileWidth + this.tileSpacing);
                const y = startY + tile.row * (this.tileHeight + this.tileSpacing);
                const z = tile.layer * this.layerHeight;

                tile.setPosition(x, y, z);
                const element = tile.createElement();

                // Update tile appearance based on blocked status
                if (tile.blocked) {
                    element.classList.add('blocked');
                } else {
                    element.classList.remove('blocked');
                }

                // Add click event listener only if not already added
                if (!element.hasAttribute('data-click-bound')) {
                    element.addEventListener('click', (e) => this.handleTileClick(tile, e));
                    element.setAttribute('data-click-bound', 'true');
                }

                this.gameBoard.appendChild(element);
            }
        });
    }

    /**
     * Handle tile click
     */
    handleTileClick(tile, event) {
        if (!this.isGameActive || tile.removed || tile.blocked) return;

        event.preventDefault();
        event.stopPropagation();

        // If tile is disabled, ignore click
        if (tile.element && tile.element.classList.contains('disabled')) {
            return;
        }

        if (this.selectedTiles.length === 0) {
            // First tile selection
            this.selectTile(tile);
            this.updateTileStates();
        } else if (this.selectedTiles.length === 1) {
            const firstTile = this.selectedTiles[0];

            if (tile === firstTile) {
                // Clicking same tile - deselect
                this.deselectTile(tile);
                this.clearTileStates();
            } else {
                // Second tile selection - check connection
                this.selectTile(tile);
                this.checkConnection();
            }
        }
    }

    /**
     * Select a tile
     */
    selectTile(tile) {
        tile.setSelected(true);
        this.selectedTiles.push(tile);
    }

    /**
     * Deselect a tile
     */
    deselectTile(tile) {
        tile.setSelected(false);
        const index = this.selectedTiles.indexOf(tile);
        if (index > -1) {
            this.selectedTiles.splice(index, 1);
        }
    }

    /**
     * Update tile states based on current selection
     */
    updateTileStates() {
        if (this.selectedTiles.length !== 1) return;

        const selectedTile = this.selectedTiles[0];
        const activeTiles = this.tileSet.tiles.filter(tile => !tile.removed);

        activeTiles.forEach(tile => {
            if (tile === selectedTile) return;

            // Check if this tile can connect with the selected tile (path connection only)
            const canConnect = this.tileSet.canConnect(selectedTile, tile);

            if (canConnect) {
                // Mark as connectable
                if (tile.element) {
                    tile.element.classList.add('connectable');
                    tile.element.classList.remove('disabled');
                }
            } else {
                // Mark as disabled
                if (tile.element) {
                    tile.element.classList.add('disabled');
                    tile.element.classList.remove('connectable');
                }
            }
        });
    }

    /**
     * Clear all tile states
     */
    clearTileStates() {
        const activeTiles = this.tileSet.tiles.filter(tile => !tile.removed);

        activeTiles.forEach(tile => {
            if (tile.element) {
                tile.element.classList.remove('disabled', 'connectable');
            }
        });
    }

    /**
     * Check if selected tiles can be connected
     */
    checkConnection() {
        if (this.selectedTiles.length !== 2) return;

        const [tile1, tile2] = this.selectedTiles;

        if (tile1.canMatch(tile2)) {
            const connectionResult = this.tileSet.canConnect(tile1, tile2);

            if (connectionResult) {
                // Valid connection found - remove tiles immediately
                this.clearTileStates();
                this.removeTiles(tile1, tile2);
            } else {
                // No valid path
                this.showInvalidConnection();
            }
        } else {
            // Tiles don't match
            this.showInvalidConnection();
        }
    }



    /**
     * Show invalid connection feedback
     */
    showInvalidConnection() {
        this.selectedTiles.forEach(tile => {
            tile.element.style.animation = 'shake 0.5s ease-in-out';
            setTimeout(() => {
                if (tile.element) {
                    tile.element.style.animation = '';
                }
            }, 500);
        });

        setTimeout(() => {
            this.selectedTiles.forEach(tile => this.deselectTile(tile));
            this.clearTileStates();
        }, 600);
    }

    /**
     * Remove matched tiles
     */
    removeTiles(tile1, tile2) {
        this.tileSet.removeTile(tile1);
        this.tileSet.removeTile(tile2);
        
        this.selectedTiles = [];
        this.score += 10;
        this.updateGameStats();

        // Update blocked status after removing tiles
        this.tileSet.updateBlockedStatus();

        // Update visual appearance of all tiles
        this.tileSet.tiles.forEach(tile => {
            if (!tile.removed && tile.element) {
                if (tile.blocked) {
                    tile.element.classList.add('blocked');
                } else {
                    tile.element.classList.remove('blocked');
                }
            }
        });

        // Check win condition
        if (this.tileSet.isComplete()) {
            this.gameWon();
        } else if (!this.tileSet.hasValidMoves()) {
            this.gameOver();
        }
    }

    /**
     * Start game timer
     */
    startTimer() {
        this.startTime = Date.now();
        this.gameTimer = setInterval(() => {
            this.gameTime = Math.floor((Date.now() - this.startTime) / 1000);
            this.updateTimeDisplay();
        }, 1000);
    }

    /**
     * Update time display
     */
    updateTimeDisplay() {
        const minutes = Math.floor(this.gameTime / 60);
        const seconds = this.gameTime % 60;
        const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        document.getElementById('timeDisplay').textContent = `Time: ${timeString}`;
    }

    /**
     * Update game statistics display
     */
    updateGameStats() {
        document.getElementById('scoreDisplay').textContent = `Score: ${this.score}`;
        document.getElementById('tilesLeft').textContent = `Tiles: ${this.tileSet.getRemainingTilesCount()}`;
    }

    /**
     * Show hint
     */
    showHint() {
        if (!this.isGameActive) return;

        // Clear any current selection and states
        this.selectedTiles.forEach(tile => this.deselectTile(tile));
        this.clearTileStates();

        const hint = this.tileSet.getHint();
        if (hint) {
            this.hintsUsed++;
            this.score = Math.max(0, this.score - 5);
            this.updateGameStats();

            hint.forEach(tile => {
                tile.element.classList.add('hint-highlight');
                setTimeout(() => {
                    if (tile.element) {
                        tile.element.classList.remove('hint-highlight');
                    }
                }, 2000);
            });
        }
    }

    /**
     * Game won
     */
    gameWon() {
        this.isGameActive = false;
        if (this.gameTimer) {
            clearInterval(this.gameTimer);
        }
        
        const timeBonus = Math.max(0, 300 - this.gameTime) * 2;
        this.score += timeBonus;
        this.updateGameStats();
        
        this.showGameMessage('Congratulations!', 
            `You completed the game in ${this.formatTime(this.gameTime)}!<br>
             Final Score: ${this.score}<br>
             Hints Used: ${this.hintsUsed}`);
    }

    /**
     * Game over
     */
    gameOver() {
        this.isGameActive = false;
        if (this.gameTimer) {
            clearInterval(this.gameTimer);
        }
        
        this.showGameMessage('Game Over', 'No more moves available. Try again!');
    }

    /**
     * Format time for display
     */
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }

    /**
     * Show game message modal
     */
    showGameMessage(title, message) {
        document.getElementById('messageTitle').textContent = title;
        document.getElementById('messageText').innerHTML = message;
        document.getElementById('gameMessage').style.display = 'flex';
    }

    /**
     * Hide game message modal
     */
    hideGameMessage() {
        document.getElementById('gameMessage').style.display = 'none';
    }

    /**
     * Show rules modal
     */
    showRules() {
        document.getElementById('rulesModal').style.display = 'flex';
    }

    /**
     * Hide rules modal
     */
    hideRules() {
        document.getElementById('rulesModal').style.display = 'none';
    }

    /**
     * Rotate view left
     */
    rotateLeft() {
        this.currentRotation -= 90;
        if (this.currentRotation < 0) this.currentRotation = 270;
        this.updateView();
    }

    /**
     * Rotate view right
     */
    rotateRight() {
        this.currentRotation += 90;
        if (this.currentRotation >= 360) this.currentRotation = 0;
        this.updateView();
    }

    /**
     * Update 3D view
     */
    updateView() {
        if (this.gameBoard) {
            this.gameBoard.style.transform = `rotateX(-20deg) rotateY(${this.currentRotation}deg)`;
        }
    }

    /**
     * Toggle fullscreen
     */
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    /**
     * Hide loading screen
     */
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }
    }
}

// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.mahjongGame = new MahjongConnectGame();
});
